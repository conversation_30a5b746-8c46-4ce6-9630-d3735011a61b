#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import json
import time
import glob
from typing import Optional, Tuple

import requests
import rasterio
from rasterio.warp import transform_bounds

STAC_SEARCH_URL = "https://earth-search.aws.element84.com/v1/search"
COLLECTION = "sentinel-2-l2a"

REGIONS = [
    "north_africa_middle_east",
    "mediterranean",
    "central_asia",
    "europe",
    "caucasus",
    "new_zealand",
]

PREFERRED_ASSETS = ["visual", "TCI", "tci", "rendered_preview", "preview"]


def list_all_tifs(region_dir: str):
    return sorted(glob.glob(os.path.join(region_dir, "*.tif")))


def compute_bbox_wgs84(tif_path: str) -> Optional[Tuple[float, float, float, float]]:
    try:
        with rasterio.open(tif_path) as ds:
            b = ds.bounds
            src_crs = ds.crs
        if src_crs is None:
            return None
        bbox_wgs84 = transform_bounds(src_crs, "EPSG:4326", b.left, b.bottom, b.right, b.top, densify_pts=21)
        # clip to valid lon/lat ranges
        xmin, ymin, xmax, ymax = bbox_wgs84
        xmin = max(-180.0, min(180.0, xmin))
        xmax = max(-180.0, min(180.0, xmax))
        ymin = max(-90.0, min(90.0, ymin))
        ymax = max(-90.0, min(90.0, ymax))
        return (xmin, ymin, xmax, ymax)
    except Exception as e:
        print(f"[WARN] 无法读取范围: {tif_path} -> {e}")
        return None


def compute_union_bbox(region_dir: str) -> Optional[Tuple[float, float, float, float]]:
    tifs = list_all_tifs(region_dir)
    if not tifs:
        return None
    union = None
    for fp in tifs:
        bb = compute_bbox_wgs84(fp)
        if not bb:
            continue
        if union is None:
            union = list(bb)
        else:
            union[0] = min(union[0], bb[0])
            union[1] = min(union[1], bb[1])
            union[2] = max(union[2], bb[2])
            union[3] = max(union[3], bb[3])
    return tuple(union) if union else None


def stac_search_bbox(bbox, datetime_range="2019-01-01T00:00:00Z/2025-12-31T23:59:59Z", limit=100):
    params = {
        "collections": COLLECTION,
        "bbox": ",".join(map(str, bbox)),  # west,south,east,north
        "limit": str(limit),
        "datetime": datetime_range,
    }
    r = requests.get(STAC_SEARCH_URL, params=params, timeout=60, headers={"Accept":"application/geo+json"})
    if r.status_code >= 400:
        raise RuntimeError(f"STAC GET {r.status_code}: {r.text[:300]}")
    return r.json()


def choose_visual_asset(item: dict) -> Optional[Tuple[str, str]]:
    assets = item.get("assets", {})
    for key in PREFERRED_ASSETS:
        if key in assets and assets[key].get("href"):
            return key, assets[key]["href"]
    # 部分数据将TCI命名为"tci"或包含在"assets"里
    for k, v in assets.items():
        name = (k or "").lower()
        if any(s in name for s in ["visual", "tci", "preview"]):
            href = v.get("href")
            if href:
                return k, href
    return None


def download_file(url: str, out_path: str):
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    with requests.get(url, stream=True, timeout=120) as r:
        r.raise_for_status()
        with open(out_path, "wb") as f:
            for chunk in r.iter_content(chunk_size=1 << 20):
                if chunk:
                    f.write(chunk)
    return out_path


def main():
    base = "regions"
    summary = []
    for region in REGIONS:
        region_dir = os.path.join(base, region)
        if not os.path.isdir(region_dir):
            continue
        bbox = compute_union_bbox(region_dir)
        if not bbox:
            print(f"[SKIP] {region}: 无法计算WGS84范围")
            continue
        print(f"[INFO] {region}: bbox(WGS84)={bbox}")
        try:
            stac = stac_search_bbox(bbox, limit=50)
        except Exception as e:
            print(f"[ERR] STAC搜索失败 {region}: {e}")
            continue
        features = stac.get("features", [])
        if not features:
            print(f"[WARN] {region}: 未检索到Sentinel-2条目")
            continue
        # 本地按云量排序（兼容不同字段名）
        def get_cloud(p):
            props = p.get("properties", {})
            for k in ["eo:cloud_cover", "cloud_cover", "s2:cloud_cover"]:
                if k in props and props[k] is not None:
                    return props[k]
            return 1000
        features.sort(key=get_cloud)
        out_dir = os.path.join(region_dir, "aws_s2")
        os.makedirs(out_dir, exist_ok=True)
        grabbed = 0
        downloaded_files = set()  # 跟踪已下载的文件，避免重复

        for item in features:
            asset = choose_visual_asset(item)
            if not asset:
                continue
            key, href = asset
            date_str = item.get("properties", {}).get("datetime", "").split("T")[0]
            # 添加item ID以确保文件名唯一性
            item_id = item.get("id", "unknown")
            out_name = f"s2_{date_str}_{item_id}_{key}.tif" if date_str else f"s2_{item_id}_{key}.tif"
            out_path = os.path.join(out_dir, out_name)

            # 检查是否已经下载过这个文件
            if out_path in downloaded_files:
                continue
            if os.path.exists(out_path) and os.path.getsize(out_path) > 1024*1024:
                summary.append((region, out_path))
                downloaded_files.add(out_path)
                continue

            print(f"[DL ] {region}: {href} -> {out_path}")
            try:
                download_file(href, out_path)
                summary.append((region, out_path))
                downloaded_files.add(out_path)
                grabbed += 1
            except Exception as e:
                print(f"[ERR] 下载失败 {region}: {e}")
            if grabbed >= 10:  # 减少下载数量避免重复
                break
            time.sleep(1.0)  # 增加延迟避免网络问题

    print("\n=== 下载完成 ===")
    for region, path in summary:
        print(f"{region}: {os.path.relpath(path)}")

if __name__ == "__main__":
    main()
