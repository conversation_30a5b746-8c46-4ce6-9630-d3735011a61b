#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版地理区域数据可视化脚本
展示各个区域的土地利用、DEM和卫星影像数据
"""

import os
import glob
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import rasterio
from rasterio.plot import show

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SimpleRegionVisualizer:
    def __init__(self):
        self.regions = {
            'north_africa_middle_east': '北非/中东',
            'mediterranean': '地中海',
            'central_asia': '中亚',
            'europe': '欧洲',
            'caucasus': '高加索',
            'new_zealand': '新西兰'
        }
        
        self.colors = {
            'north_africa_middle_east': '#FF6B6B',
            'mediterranean': '#4ECDC4',
            'central_asia': '#45B7D1',
            'europe': '#96CEB4',
            'caucasus': '#FFEAA7',
            'new_zealand': '#DDA0DD'
        }
    
    def get_region_files(self, region_name):
        """获取指定区域的文件列表"""
        region_path = f"regions/{region_name}"
        if not os.path.exists(region_path):
            return []
        
        files = {
            'tif': glob.glob(f"{region_path}/*.tif"),
            'shp': glob.glob(f"{region_path}/*.shp"),
            'xml': glob.glob(f"{region_path}/*.xml")
        }
        return files
    
    def plot_region_overview(self):
        """绘制区域概览图（简化版）"""
        fig, ax = plt.subplots(figsize=(15, 10))
        
        # 创建简单的世界地图背景
        ax.set_xlim(-20, 180)
        ax.set_ylim(-60, 80)
        
        # 添加网格
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('经度 (°)', fontsize=12)
        ax.set_ylabel('纬度 (°)', fontsize=12)
        
        # 定义各区域的大致边界框
        region_bounds = {
            'north_africa_middle_east': [-10, 50, 20, 40],  # [lon_min, lon_max, lat_min, lat_max]
            'mediterranean': [-10, 45, 30, 50],
            'central_asia': [50, 100, 30, 60],
            'europe': [-10, 30, 35, 70],
            'caucasus': [40, 50, 40, 45],
            'new_zealand': [165, 175, -48, -34]
        }
        
        # 绘制区域边界框
        for region, bounds in region_bounds.items():
            lon_min, lon_max, lat_min, lat_max = bounds
            rect = patches.Rectangle((lon_min, lat_min), 
                                   lon_max - lon_min, 
                                   lat_max - lat_min,
                                   linewidth=2, 
                                   edgecolor=self.colors[region],
                                   facecolor=self.colors[region],
                                   alpha=0.3)
            ax.add_patch(rect)
            
            # 添加区域标签
            ax.text((lon_min + lon_max) / 2, (lat_min + lat_max) / 2,
                   self.regions[region], 
                   ha='center', va='center', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        ax.set_title('全球研究区域分布图', fontsize=16, fontweight='bold', pad=20)
        plt.tight_layout()
        return fig
    
    def plot_region_details(self, region_name):
        """绘制指定区域的详细数据"""
        files = self.get_region_files(region_name)
        if not files['tif']:
            print(f"区域 {region_name} 没有找到TIF文件")
            return None
        
        # 计算子图数量
        n_files = len(files['tif'])
        n_cols = min(3, n_files)
        n_rows = (n_files + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5 * n_rows))
        
        # 处理子图数组
        if n_rows == 1 and n_cols == 1:
            axes = [[axes]]
        elif n_rows == 1:
            axes = [axes]
        elif n_cols == 1:
            axes = [[ax] for ax in axes]
        
        fig.suptitle(f'{self.regions[region_name]} 区域数据', fontsize=16, fontweight='bold')
        
        for i, tif_file in enumerate(sorted(files['tif'])):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row][col]
            
            try:
                with rasterio.open(tif_file) as src:
                    # 读取数据
                    data = src.read(1)
                    
                    # 创建掩码处理无效值
                    mask = (data == src.nodata) if src.nodata else (data == 0)
                    data_masked = np.ma.masked_where(mask, data)
                    
                    # 绘制栅格数据
                    im = ax.imshow(data_masked, cmap='viridis', aspect='auto')
                    
                    # 设置标题
                    title = os.path.basename(tif_file).replace('.tif', '')
                    ax.set_title(title, fontsize=10)
                    ax.axis('off')
                    
                    # 添加颜色条
                    plt.colorbar(im, ax=ax, shrink=0.8)
                    
            except Exception as e:
                ax.text(0.5, 0.5, f'无法读取\n{os.path.basename(tif_file)}', 
                       ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'错误: {str(e)[:30]}...', fontsize=8)
                ax.axis('off')
        
        # 隐藏多余的子图
        for i in range(n_files, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            axes[row][col].axis('off')
        
        plt.tight_layout()
        return fig
    
    def create_summary_report(self):
        """创建数据汇总报告"""
        print("=== 地理区域数据汇总报告 ===\n")
        
        total_files = 0
        for region_name, region_cn in self.regions.items():
            files = self.get_region_files(region_name)
            n_tif = len(files['tif'])
            n_shp = len(files['shp'])
            n_xml = len(files['xml'])
            
            total_files += n_tif + n_shp + n_xml
            
            print(f"{region_cn} ({region_name}):")
            print(f"  TIF文件: {n_tif} 个")
            print(f"  Shapefile: {n_shp} 个")
            print(f"  XML文件: {n_xml} 个")
            
            if n_tif > 0:
                print(f"  主要数据文件:")
                for tif_file in sorted(files['tif'])[:3]:  # 只显示前3个
                    filename = os.path.basename(tif_file)
                    print(f"    - {filename}")
                if n_tif > 3:
                    print(f"    ... 还有 {n_tif - 3} 个文件")
            print()
        
        print(f"总计: {total_files} 个文件")
    
    def run_visualization(self):
        """运行完整的可视化流程"""
        # 创建汇总报告
        self.create_summary_report()
        
        # 绘制全球概览图
        print("正在生成全球概览图...")
        overview_fig = self.plot_region_overview()
        overview_fig.savefig('global_regions_overview.png', dpi=300, bbox_inches='tight')
        print("全球概览图已保存为: global_regions_overview.png")
        
        # 为每个区域生成详细图
        for region_name in self.regions.keys():
            print(f"正在生成 {self.regions[region_name]} 区域详细图...")
            detail_fig = self.plot_region_details(region_name)
            if detail_fig:
                filename = f'region_{region_name}_details.png'
                detail_fig.savefig(filename, dpi=300, bbox_inches='tight')
                print(f"{self.regions[region_name]} 详细图已保存为: {filename}")
            plt.close('all')
        
        print("\n所有可视化图表已生成完成！")

def main():
    """主函数"""
    print("开始地理区域数据可视化（简化版）...")
    
    visualizer = SimpleRegionVisualizer()
    visualizer.run_visualization()
    
    print("\n可视化完成！")

if __name__ == "__main__":
    main()
