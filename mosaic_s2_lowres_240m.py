#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import glob
import math
import numpy as np
import rasterio
from rasterio.warp import reproject, Resampling, transform_bounds
from rasterio.crs import CRS
from rasterio.merge import merge
from rasterio.transform import from_origin
import matplotlib.pyplot as plt

REGIONS = [
    "north_africa_middle_east",
    "mediterranean",
    "central_asia",
    "europe",
    "caucasus",
    "new_zealand",
]

plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Noto Sans CJK SC']
plt.rcParams['axes.unicode_minus'] = False

PREFERRED_REF_PATTERNS = [
    "*2010lc030.tif",  # LULC
    "*dem*.tif",       # DEM
    "*.tif",
]


def list_ref_tifs(region_dir: str):
    for pat in PREFERRED_REF_PATTERNS:
        files = sorted(glob.glob(os.path.join(region_dir, pat)))
        if files:
            return files
    return []


def get_ref_grid(region_dir: str, target_res_m: float):
    ref_files = list_ref_tifs(region_dir)
    if not ref_files:
        return None
    # 取第一个作为网格参考（投影+范围），并用所有同投影栅格求联合范围
    with rasterio.open(ref_files[0]) as ref:
        dst_crs = ref.crs
        if dst_crs is None:
            return None
        left, bottom, right, top = ref.bounds
        # 合并同投影文件的范围
        for fp in ref_files[1:]:
            try:
                with rasterio.open(fp) as ds2:
                    if ds2.crs == dst_crs:
                        l, b, r, t = ds2.bounds
                        left, bottom = min(left, l), min(bottom, b)
                        right, top = max(right, r), max(top, t)
            except Exception:
                continue
        # 估算像元大小单位是否为米（投影通常为UTM）
        # 直接使用目标分辨率（米）
        xres = target_res_m
        yres = target_res_m
        width = int(math.ceil((right - left) / xres))
        height = int(math.ceil((top - bottom) / yres))
        transform = from_origin(left, top, xres, yres)
        return {
            'crs': dst_crs,
            'transform': transform,
            'width': width,
            'height': height,
            'bounds': (left, bottom, right, top)
        }


def search_aws_s2_assets(region_dir: str):
    # 读取前面脚本下载到 aws_s2 下的 COG 列表（避免再次网络检索）
    aws_dir = os.path.join(region_dir, 'aws_s2')
    return sorted(glob.glob(os.path.join(aws_dir, '*.tif')))


def assets_union_bounds_in_crs(asset_paths, dst_crs):
    union = None
    for fp in asset_paths:
        try:
            with rasterio.open(fp) as ds:
                b = ds.bounds
                if ds.crs != dst_crs:
                    b = transform_bounds(ds.crs, dst_crs, b.left, b.bottom, b.right, b.top, densify_pts=21)
                if union is None:
                    union = list(b)
                else:
                    union[0] = min(union[0], b[0])
                    union[1] = min(union[1], b[1])
                    union[2] = max(union[2], b[2])
                    union[3] = max(union[3], b[3])
        except Exception:
            continue
    return tuple(union) if union else None


def reproject_cog_to_grid(href: str, grid, dst_nodata=np.nan):
    """将远程/本地 COG 按目标网格重投影。返回 (3, H, W) 数组，若失败返回 None。"""
    try:
        with rasterio.open(href) as src:
            # 目标数组 3 波段，单波段时后续复制
            bands = min(3, src.count) if src.count >= 1 else 0
            if bands == 0:
                return None
            dst = np.full((3, grid['height'], grid['width']), dst_nodata, dtype=np.float32)
            for bi in range(1, bands + 1):
                tmp = np.full((grid['height'], grid['width']), dst_nodata, dtype=np.float32)
                reproject(
                    source=rasterio.band(src, bi),
                    destination=tmp,
                    src_transform=src.transform,
                    src_crs=src.crs,
                    dst_transform=grid['transform'],
                    dst_crs=grid['crs'],
                    resampling=Resampling.bilinear,
                    src_nodata=0,
                    dst_nodata=dst_nodata,
                    num_threads=2,
                )
                dst[bi - 1] = tmp
            # 若只有1波段，则复制到RGB
            if bands == 1:
                dst[1] = dst[0]
                dst[2] = dst[0]
            return dst
    except Exception as e:
        print(f"[WARN] 读取失败，跳过: {href} -> {e}")
        return None


def percentile_stretch_rgb(arr):
    vals = arr.reshape(-1, 3)
    mask = np.all(np.isfinite(vals), axis=1)
    if not np.any(mask):
        return np.zeros_like(arr)
    valid = vals[mask]
    p1 = np.percentile(valid, 1, axis=0)
    p99 = np.percentile(valid, 99, axis=0)
    p99 = np.where(p99 <= p1, p1 + 1e-6, p99)
    out = (arr - p1) / (p99 - p1)
    return np.clip(out, 0, 1)


def mosaic_region(region, target_res_m=240.0):
    region_dir = os.path.join('regions', region)
    assets = search_aws_s2_assets(region_dir)
    if not assets:
        print(f"[SKIP] {region}: 未发现 AWS S2 资产，请先运行 aws_fetch_s2.py")
        return None

    # 统一到 WGS84 经纬度
    dst_crs = CRS.from_epsg(4326)
    # 参考范围（如果有） -> 3857
    ref_grid = get_ref_grid(region_dir, target_res_m)
    ref_bounds_3857 = None
    if ref_grid:
        l, b, r, t = ref_grid['bounds']
        ref_bounds_3857 = transform_bounds(ref_grid['crs'], dst_crs, l, b, r, t, densify_pts=21)
    # 资产联合范围 -> 4326
    a_union_3857 = assets_union_bounds_in_crs(assets, dst_crs)
    if not a_union_3857:
        print(f"[SKIP] {region}: 资产范围不可用")
        return None
    # 求交
    if ref_bounds_3857:
        l0, b0, r0, t0 = ref_bounds_3857
    else:
        l0, b0, r0, t0 = (-180.0, -85.0, 180.0, 85.0)
    l1, b1, r1, t1 = a_union_3857
    left = max(l0, l1)
    bottom = max(b0, b1)
    right = min(r0, r1)
    top = min(t0, t1)
    if not (right > left and top > bottom):
        print(f"[SKIP] {region}: 参考范围与资产无交集")
        return None
    # 将240m换算为度分辨率
    import math
    lat_c = (top + bottom) / 2.0
    yres = target_res_m / 111320.0
    xres = target_res_m / (111320.0 * max(0.1, math.cos(math.radians(lat_c))))
    width = int(math.ceil((right - left) / xres))
    height = int(math.ceil((top - bottom) / yres))
    grid = {
        'crs': dst_crs,
        'transform': from_origin(left, top, xres, yres),
        'width': width,
        'height': height,
        'bounds': (left, bottom, right, top)
    }

    mosaic = np.full((3, grid['height'], grid['width']), np.nan, dtype=np.float32)
    filled_mask = np.zeros((grid['height'], grid['width']), dtype=bool)

    for href in assets:
        arr = reproject_cog_to_grid(href, grid)
        if arr is None:
            continue
        # 以“先到先用”方式填补空白像元
        valid = np.isfinite(arr[0]) | np.isfinite(arr[1]) | np.isfinite(arr[2])
        write_idx = valid & (~filled_mask)
        if np.any(write_idx):
            for c in range(3):
                band = arr[c]
                band = np.where(write_idx, band, mosaic[c])
                mosaic[c] = band
            filled_mask = filled_mask | valid

    out_dir = os.path.join(region_dir, 'aws_s2')
    os.makedirs(out_dir, exist_ok=True)
    out_tif = os.path.join(out_dir, 'mosaic_240m.tif')

    meta = {
        'driver': 'GTiff',
        'height': grid['height'],
        'width': grid['width'],
        'count': 3,
        'dtype': 'float32',
        'crs': grid['crs'],
        'transform': grid['transform'],
        'compress': 'deflate'
    }
    with rasterio.open(out_tif, 'w', **meta) as dst:
        dst.write(mosaic)
    print(f"[OK] {region}: 写出 {out_tif}")

    # 预览
    rgb = np.transpose(mosaic, (1, 2, 0))
    rgb = percentile_stretch_rgb(rgb)
    fig, ax = plt.subplots(figsize=(10, 8))
    ax.imshow(rgb)
    ax.set_title(f"{region} - S2 240m 拼接预览", fontsize=14)
    ax.axis('off')
    out_png = os.path.join(out_dir, 'mosaic_240m_preview.png')
    fig.savefig(out_png, dpi=200, bbox_inches='tight')
    plt.close(fig)
    print(f"[OK] {region}: 预览 {out_png}")
    return out_tif, out_png


def main():
    results = []
    for r in REGIONS:
        res = mosaic_region(r, 240.0)
        if res:
            results.append((r, ) + res)
    if results:
        print("\n=== 240m 拼接完成 ===")
        for r, tif, png in results:
            print(f"{r}: {tif} | {png}")

if __name__ == '__main__':
    main()
