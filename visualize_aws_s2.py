#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import glob
import numpy as np
import rasterio
import matplotlib.pyplot as plt

REGIONS = [
    "north_africa_middle_east",
    "mediterranean",
    "central_asia",
    "europe",
    "caucasus",
    "new_zealand",
]

plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Noto Sans CJK SC']
plt.rcParams['axes.unicode_minus'] = False

def to_rgb(ds):
    # 优先使用前3个波段作为RGB；若是uint8则/255归一化，否则做1-99百分位拉伸
    if ds.count >= 3:
        r = ds.read(1)
        g = ds.read(2)
        b = ds.read(3)
        arr = np.dstack([r, g, b]).astype(np.float32)
        if np.issubdtype(arr.dtype, np.integer) and arr.max() > 0 and arr.max() <= 255:
            arr = arr / 255.0
        else:
            # 百分位拉伸
            vals = arr.reshape(-1, 3)
            valid = vals[np.all(np.isfinite(vals), axis=1)]
            if valid.size > 0:
                p1 = np.percentile(valid, 1, axis=0)
                p99 = np.percentile(valid, 99, axis=0)
                p99 = np.where(p99 <= p1, p1 + 1e-6, p99)
                for i in range(3):
                    arr[..., i] = (arr[..., i] - p1[i]) / (p99[i] - p1[i])
                arr = np.clip(arr, 0, 1)
            else:
                arr = np.zeros_like(arr)
        return arr
    else:
        # 单波段灰度
        band = ds.read(1).astype(np.float32)
        valid = band[np.isfinite(band)]
        if valid.size > 0:
            vmin = np.percentile(valid, 1)
            vmax = np.percentile(valid, 99)
            if vmax <= vmin:
                vmax = vmin + 1e-6
            band = (band - vmin) / (vmax - vmin)
            band = np.clip(band, 0, 1)
        else:
            band = np.zeros_like(band)
        return np.dstack([band, band, band])


def visualize_region(region):
    aws_dir = os.path.join('regions', region, 'aws_s2')
    tif_list = sorted(glob.glob(os.path.join(aws_dir, '*.tif')))
    if not tif_list:
        return None
    n = len(tif_list)
    ncols = min(3, n)
    nrows = (n + ncols - 1) // ncols
    fig, axes = plt.subplots(nrows, ncols, figsize=(5*ncols, 4*nrows))
    if nrows == 1 and ncols == 1:
        axes = np.array([[axes]])
    elif nrows == 1:
        axes = np.array([axes])
    elif ncols == 1:
        axes = np.array([[ax] for ax in axes])

    idx = 0
    for r in range(nrows):
        for c in range(ncols):
            ax = axes[r][c]
            if idx < n:
                fp = tif_list[idx]
                try:
                    with rasterio.open(fp) as ds:
                        # 先做一个快速探测读取，若失败则跳过该文件
                        try:
                            _ = ds.read(1, window=((0, 1), (0, 1)))
                        except Exception:
                            raise
                        rgb = to_rgb(ds)
                    ax.imshow(rgb)
                    ax.set_title(os.path.basename(fp), fontsize=10)
                    ax.axis('off')
                except Exception as e:
                    ax.text(0.5, 0.5, f"跳过\n{os.path.basename(fp)}\n{str(e).splitlines()[0][:40]}",
                            ha='center', va='center', fontsize=8)
                    ax.axis('off')
                idx += 1
            else:
                ax.axis('off')
    fig.suptitle(f"{region} 区域 - AWS Sentinel-2 预览", fontsize=14)
    plt.tight_layout()
    out_root = f"region_{region}_aws_s2.png"
    fig.savefig(out_root, dpi=200, bbox_inches='tight')
    # 同步一份到区域目录
    fig.savefig(os.path.join(aws_dir, 'aws_s2_preview.png'), dpi=200, bbox_inches='tight')
    plt.close(fig)
    return out_root


def main():
    outputs = []
    for region in REGIONS:
        out = visualize_region(region)
        if out:
            outputs.append(out)
            print(f"生成: {out}")
        else:
            print(f"跳过: {region}（无AWS影像）")
    if outputs:
        print("\n已生成预览: ")
        for o in outputs:
            print("  ", o)

if __name__ == '__main__':
    main()
