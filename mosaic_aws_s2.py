#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import glob
import numpy as np
import rasterio
from rasterio.merge import merge
from rasterio.enums import Resampling
import matplotlib.pyplot as plt

REGIONS = [
    "north_africa_middle_east",
    "mediterranean",
    "central_asia",
    "europe",
    "caucasus",
    "new_zealand",
]

plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Noto Sans CJK SC']
plt.rcParams['axes.unicode_minus'] = False


def percentile_stretch_rgb(arr):
    # arr: HxWx3 float/uint array
    out = arr.astype(np.float32).copy()
    h, w, c = out.shape
    vals = out.reshape(-1, c)
    valid = vals[np.all(np.isfinite(vals), axis=1)]
    if valid.size == 0:
        return np.zeros_like(out)
    p1 = np.percentile(valid, 1, axis=0)
    p99 = np.percentile(valid, 99, axis=0)
    p99 = np.where(p99 <= p1, p1 + 1e-6, p99)
    for i in range(c):
        out[..., i] = (out[..., i] - p1[i]) / (p99[i] - p1[i])
    return np.clip(out, 0, 1)


def mosaic_region(region):
    aws_dir = os.path.join('regions', region, 'aws_s2')
    tif_list = sorted(glob.glob(os.path.join(aws_dir, '*.tif')))
    if not tif_list:
        print(f"[SKIP] {region}: 没有AWS影像")
        return None
    srcs = []
    try:
        for fp in tif_list:
            try:
                src = rasterio.open(fp)
                # 可读性检测：尝试缩略读取，失败则视为损坏
                try:
                    h, w = src.height, src.width
                    th = min(512, max(1, h))
                    tw = min(512, max(1, w))
                    _ = src.read(1, out_shape=(1, th, tw))
                except Exception as e:
                    raise RuntimeError(f"不可读取: {e}")
                srcs.append(src)
            except Exception as e:
                print(f"[WARN] 跳过损坏文件: {fp} -> {e}")
        if not srcs:
            print(f"[SKIP] {region}: 无可用影像")
            return None
        mosaic, out_transform = merge(srcs, method='first')
        out_meta = srcs[0].meta.copy()
        out_meta.update({
            'height': mosaic.shape[1],
            'width': mosaic.shape[2],
            'transform': out_transform
        })
    finally:
        for s in srcs:
            try:
                s.close()
            except Exception:
                pass

    out_tif = os.path.join(aws_dir, 'mosaic.tif')
    with rasterio.open(out_tif, 'w', **out_meta) as dst:
        dst.write(mosaic)
    print(f"[OK] {region}: 写出 {out_tif}")

    # 预览
    # 若>=3波段取前3；否则单波段灰度
    if mosaic.shape[0] >= 3:
        rgb = np.dstack([mosaic[0], mosaic[1], mosaic[2]])
        # 若是整数且范围>255，做百分位拉伸
        if np.issubdtype(rgb.dtype, np.integer) and rgb.max() > 255:
            rgb = percentile_stretch_rgb(rgb)
        else:
            rgb = np.clip(rgb / 255.0, 0, 1)
    else:
        band = mosaic[0].astype(np.float32)
        v = band[np.isfinite(band)]
        if v.size > 0:
            vmin, vmax = np.percentile(v, 1), np.percentile(v, 99)
            vmax = max(vmax, vmin + 1e-6)
            band = np.clip((band - vmin) / (vmax - vmin), 0, 1)
        rgb = np.dstack([band, band, band])

    fig, ax = plt.subplots(figsize=(10, 8))
    ax.imshow(rgb)
    ax.set_title(f"{region} - AWS S2 拼接预览", fontsize=14)
    ax.axis('off')
    out_png = os.path.join(aws_dir, 'mosaic_preview.png')
    fig.savefig(out_png, dpi=200, bbox_inches='tight')
    plt.close(fig)
    print(f"[OK] {region}: 预览 {out_png}")
    return out_tif, out_png


def main():
    results = []
    for r in REGIONS:
        res = mosaic_region(r)
        if res:
            results.append((r, ) + res)
    if results:
        print("\n=== 拼接完成 ===")
        for r, tif, png in results:
            print(f"{r}: {tif} | {png}")

if __name__ == '__main__':
    main()
